// File: Program.cs

using System.Net;
using System.Net.Sockets;

// Main program entry point and server logic
await StartServer();

async Task StartServer()
{
    using var udpClient = new UdpClient(new IPEndPoint(IPAddress.Any, 2053));
    Console.WriteLine("DNS server listening on port 2053...");

    try
    {
        while (true)
        {
            UdpReceiveResult receiveResult = await udpClient.ReceiveAsync();
            Console.WriteLine($"Received packet from {receiveResult.RemoteEndPoint}");

            // For this stage, we ignore the incoming packet's content.
            // We construct a response with a hardcoded header as per the specification.

            var responseMessage = new DnsMessage
            {
                Header = new DnsHeader
                {
                    PacketIdentifier = 1234,
                    QueryResponse = true, // This is a response
                    OpCode = 0, // Standard query
                    AuthoritativeAnswer = false,
                    Truncation = false,
                    RecursionDesired = false, // We are not requesting recursion
                    RecursionAvailable = false, // We do not offer recursion
                    ResponseCode = 0, // No error
                    QuestionCount = 0,
                    AnswerRecordCount = 0,
                    AuthorityRecordCount = 0,
                    AdditionalRecordCount = 0
                }
            };

            // Serialize the DnsMessage object into a byte array.
            byte[] responseBytes = DnsPacketSerializer.ToByteArray(responseMessage);

            // Send the serialized response back to the client.
            await udpClient.SendAsync(responseBytes, responseBytes.Length, receiveResult.RemoteEndPoint);
            Console.WriteLine($"Sent header response to {receiveResult.RemoteEndPoint}");
        }
    }
    catch (SocketException e)
    {
        Console.WriteLine($"SocketException: {e.Message}");
    }
}

/// <summary>
/// Represents the 12-byte header of a DNS message.
/// </summary>
public class DnsHeader
{
    public ushort PacketIdentifier { get; set; } // 16 bits
    public bool QueryResponse { get; set; }      // 1 bit (0 for query, 1 for response)
    public byte OpCode { get; set; }             // 4 bits (0 for standard query)
    public bool AuthoritativeAnswer { get; set; }// 1 bit
    public bool Truncation { get; set; }         // 1 bit
    public bool RecursionDesired { get; set; }   // 1 bit
    public bool RecursionAvailable { get; set; }// 1 bit
    public byte ResponseCode { get; set; }       // 4 bits (0 for no error)
    public ushort QuestionCount { get; set; }    // 16 bits
    public ushort AnswerRecordCount { get; set; }// 16 bits
    public ushort AuthorityRecordCount { get; set; } // 16 bits
    public ushort AdditionalRecordCount { get; set; } // 16 bits
}

/// <summary>
/// Represents a full DNS message, including header, questions, and answers.
/// </summary>
public class DnsMessage
{
    public DnsHeader Header { get; set; } = new();
    // Question, Answer, etc. sections will be added in later stages.
}

/// <summary>
/// Handles serialization of DNS message objects into byte arrays.
/// </summary>
public static class DnsPacketSerializer
{
    public static byte[] ToByteArray(DnsMessage message)
    {
        var stream = new MemoryStream();
        // Using a BinaryWriter helps, but we must manage endianness manually for network order.
        using (var writer = new BinaryWriter(stream))
        {
            // --- Header Section (12 bytes) ---

            // Packet Identifier (ID) - 16 bits
            writer.Write(IPAddress.HostToNetworkOrder((short)message.Header.PacketIdentifier));

            // Flags - 16 bits total, split into two bytes
            ushort flags = 0;
            if (message.Header.QueryResponse) flags |= (1 << 15);
            flags |= (ushort)(message.Header.OpCode << 11);
            if (message.Header.AuthoritativeAnswer) flags |= (1 << 10);
            if (message.Header.Truncation) flags |= (1 << 9);
            if (message.Header.RecursionDesired) flags |= (1 << 8);
            if (message.Header.RecursionAvailable) flags |= (1 << 7);
            flags |= message.Header.ResponseCode;
            writer.Write(IPAddress.HostToNetworkOrder((short)flags));

            // Question Count (QDCOUNT) - 16 bits
            writer.Write(IPAddress.HostToNetworkOrder((short)message.Header.QuestionCount));

            // Answer Record Count (ANCOUNT) - 16 bits
            writer.Write(IPAddress.HostToNetworkOrder((short)message.Header.AnswerRecordCount));

            // Authority Record Count (NSCOUNT) - 16 bits
            writer.Write(IPAddress.HostToNetworkOrder((short)message.Header.AuthorityRecordCount));

            // Additional Record Count (ARCOUNT) - 16 bits
            writer.Write(IPAddress.HostToNetworkOrder((short)message.Header.AdditionalRecordCount));
        }
        return stream.ToArray();
    }
}
