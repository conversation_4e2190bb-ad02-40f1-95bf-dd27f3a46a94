# Comprehensive test for DNS server Stage 2
Write-Host "=== Comprehensive DNS Server Stage 2 Test ==="

try {
    $udp = New-Object System.Net.Sockets.UdpClient
    $endpoint = New-Object System.Net.IPEndPoint([System.Net.IPAddress]::Loopback, 2053)
    
    # Test 1: Basic functionality
    Write-Host "`nTest 1: Basic DNS Query"
    $testData = @(0x12, 0x34, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00)
    $sent = $udp.Send($testData, $testData.Length, $endpoint)
    $response = $udp.Receive([ref]$endpoint)
    
    Write-Host "Sent: $([System.BitConverter]::ToString($testData))"
    Write-Host "Received: $([System.BitConverter]::ToString($response))"
    
    # Verify Stage 2 requirements
    $expected = @(0x04, 0xD2, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00)
    $matches = $true
    for ($i = 0; $i -lt $expected.Length; $i++) {
        if ($response[$i] -ne $expected[$i]) {
            $matches = $false
            break
        }
    }
    
    if ($matches) {
        Write-Host "✓ Response matches Stage 2 requirements exactly!"
    } else {
        Write-Host "✗ Response does not match Stage 2 requirements"
        Write-Host "Expected: $([System.BitConverter]::ToString($expected))"
    }
    
    # Parse the response header
    Write-Host "`n=== DNS Header Analysis ==="
    $packetId = ([int]$response[0] -shl 8) -bor [int]$response[1]
    $flags = ([int]$response[2] -shl 8) -bor [int]$response[3]
    $questionCount = ($response[4] -shl 8) -bor $response[5]
    $answerCount = ($response[6] -shl 8) -bor $response[7]
    $authorityCount = ($response[8] -shl 8) -bor $response[9]
    $additionalCount = ($response[10] -shl 8) -bor $response[11]
    
    Write-Host "Packet ID: $packetId (0x$($packetId.ToString('X4'))) - Expected: 1234"
    Write-Host "Flags: 0x$($flags.ToString('X4')) - Expected: 0x8000"
    
    # Parse individual flags
    $qr = ($flags -band 0x8000) -ne 0
    $opcode = ($flags -shr 11) -band 0x0F
    $aa = ($flags -band 0x0400) -ne 0
    $tc = ($flags -band 0x0200) -ne 0
    $rd = ($flags -band 0x0100) -ne 0
    $ra = ($flags -band 0x0080) -ne 0
    $rcode = $flags -band 0x000F
    
    Write-Host "QR (Query/Response): $(if ($qr) { 'Response' } else { 'Query' }) - Expected: Response"
    Write-Host "OPCODE: $opcode - Expected: 0"
    Write-Host "AA (Authoritative): $aa - Expected: False"
    Write-Host "TC (Truncated): $tc - Expected: False"
    Write-Host "RD (Recursion Desired): $rd - Expected: False"
    Write-Host "RA (Recursion Available): $ra - Expected: False"
    Write-Host "RCODE: $rcode - Expected: 0"
    Write-Host "Question Count: $questionCount - Expected: 0"
    Write-Host "Answer Count: $answerCount - Expected: 0"
    Write-Host "Authority Count: $authorityCount - Expected: 0"
    Write-Host "Additional Count: $additionalCount - Expected: 0"
    
    # Verify all Stage 2 requirements
    $allCorrect = ($packetId -eq 1234) -and $qr -and ($opcode -eq 0) -and (-not $aa) -and (-not $tc) -and (-not $rd) -and (-not $ra) -and ($rcode -eq 0) -and ($questionCount -eq 0) -and ($answerCount -eq 0) -and ($authorityCount -eq 0) -and ($additionalCount -eq 0)
    
    Write-Host "`n=== Final Verification ==="
    if ($allCorrect) {
        Write-Host "✓ ALL STAGE 2 REQUIREMENTS VERIFIED SUCCESSFULLY!"
        Write-Host "✓ DNS Header serialization is working correctly"
        Write-Host "✓ Network communication is working correctly"
        Write-Host "✓ Byte-level output is correct"
    } else {
        Write-Host "✗ Some Stage 2 requirements are not met"
    }
    
    # Test 2: Multiple requests
    Write-Host "`nTest 2: Multiple Requests"
    for ($i = 1; $i -le 3; $i++) {
        $testData2 = @(0x00, $i, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00)
        $sent2 = $udp.Send($testData2, $testData2.Length, $endpoint)
        $response2 = $udp.Receive([ref]$endpoint)
        
        # Should always return the same hardcoded response
        $matches2 = $true
        for ($j = 0; $j -lt $expected.Length; $j++) {
            if ($response2[$j] -ne $expected[$j]) {
                $matches2 = $false
                break
            }
        }
        
        if ($matches2) {
            Write-Host "✓ Request ${i}: Consistent response"
        } else {
            Write-Host "✗ Request ${i}: Inconsistent response"
        }
    }
    
    $udp.Close()
    Write-Host "`n✓ All tests completed successfully!"
    
} catch {
    Write-Host "✗ Test failed: $($_.Exception.Message)"
}

Write-Host "`n=== Test Complete ==="
