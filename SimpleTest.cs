// File: SimpleTest.cs
// Simple test to verify DNS server on port 2053

using System.Net;
using System.Net.Sockets;

Console.WriteLine("=== Testing DNS Server on Port 2053 ===");

try
{
    using var client = new UdpClient();
    var serverEndpoint = new IPEndPoint(IPAddress.Loopback, 2053);
    
    // Create a simple test packet
    byte[] testData = { 0x12, 0x34, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
    
    Console.WriteLine($"Sending test packet: {BitConverter.ToString(testData)}");
    await client.SendAsync(testData, testData.Length, serverEndpoint);
    Console.WriteLine("Packet sent successfully");
    
    // Wait for response with timeout
    var receiveTask = client.ReceiveAsync();
    var timeoutTask = Task.Delay(5000);
    
    var completedTask = await Task.WhenAny(receiveTask, timeoutTask);
    
    if (completedTask == receiveTask)
    {
        var result = await receiveTask;
        Console.WriteLine($"Received response: {result.Buffer.Length} bytes");
        Console.WriteLine($"Response: {BitConverter.ToString(result.Buffer)}");
        
        // Expected response for Stage 2
        byte[] expected = { 0x04, 0xD2, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
        
        if (result.Buffer.SequenceEqual(expected))
        {
            Console.WriteLine("✓ Response matches Stage 2 expected output!");
            
            // Parse and display the header
            ushort packetId = (ushort)((result.Buffer[0] << 8) | result.Buffer[1]);
            ushort flags = (ushort)((result.Buffer[2] << 8) | result.Buffer[3]);
            
            Console.WriteLine($"Packet ID: {packetId} (expected: 1234)");
            Console.WriteLine($"Flags: 0x{flags:X4} (expected: 0x8000)");
            Console.WriteLine($"QR flag: {((flags & 0x8000) != 0 ? "Response" : "Query")} (expected: Response)");
            
            Console.WriteLine("\n✓ Stage 2 DNS Header Implementation is WORKING CORRECTLY!");
        }
        else
        {
            Console.WriteLine("✗ Response does not match expected Stage 2 output");
            Console.WriteLine($"Expected: {BitConverter.ToString(expected)}");
            Console.WriteLine($"Actual:   {BitConverter.ToString(result.Buffer)}");
        }
    }
    else
    {
        Console.WriteLine("✗ Timeout - no response from server");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"✗ Test failed: {ex.Message}");
}

Console.WriteLine("\n=== Test Complete ===");
