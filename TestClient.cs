// File: TestClient.cs
// Simple DNS client to test our server

using System.Net;
using System.Net.Sockets;

public static class TestClient
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("=== DNS Client Test ===");
        
        try
        {
            using var client = new UdpClient();
            var serverEndpoint = new IPEndPoint(IPAddress.Loopback, 2053);
            
            // Create a simple DNS query packet (we'll just send some test data)
            byte[] queryData = { 0x12, 0x34, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
            
            Console.WriteLine($"Sending test query to DNS server: {BitConverter.ToString(queryData)}");
            await client.SendAsync(queryData, queryData.Length, serverEndpoint);
            
            // Wait for response with timeout
            var receiveTask = client.ReceiveAsync();
            var timeoutTask = Task.Delay(5000); // 5 second timeout
            
            var completedTask = await Task.WhenAny(receiveTask, timeoutTask);
            
            if (completedTask == receiveTask)
            {
                var result = await receiveTask;
                Console.WriteLine($"Received response: {result.Buffer.Length} bytes");
                Console.WriteLine($"Response data: {BitConverter.ToString(result.Buffer)}");
                
                // Analyze the response
                if (result.Buffer.Length == 12)
                {
                    Console.WriteLine("\n=== DNS Header Analysis ===");
                    
                    // Parse the header
                    ushort packetId = (ushort)((result.Buffer[0] << 8) | result.Buffer[1]);
                    ushort flags = (ushort)((result.Buffer[2] << 8) | result.Buffer[3]);
                    ushort questionCount = (ushort)((result.Buffer[4] << 8) | result.Buffer[5]);
                    ushort answerCount = (ushort)((result.Buffer[6] << 8) | result.Buffer[7]);
                    ushort authorityCount = (ushort)((result.Buffer[8] << 8) | result.Buffer[9]);
                    ushort additionalCount = (ushort)((result.Buffer[10] << 8) | result.Buffer[11]);
                    
                    Console.WriteLine($"Packet ID: {packetId} (0x{packetId:X4})");
                    Console.WriteLine($"Flags: 0x{flags:X4}");
                    
                    // Parse flags
                    bool qr = (flags & 0x8000) != 0;
                    byte opcode = (byte)((flags >> 11) & 0x0F);
                    bool aa = (flags & 0x0400) != 0;
                    bool tc = (flags & 0x0200) != 0;
                    bool rd = (flags & 0x0100) != 0;
                    bool ra = (flags & 0x0080) != 0;
                    byte rcode = (byte)(flags & 0x000F);
                    
                    Console.WriteLine($"  QR (Query/Response): {(qr ? "Response" : "Query")}");
                    Console.WriteLine($"  OPCODE: {opcode}");
                    Console.WriteLine($"  AA (Authoritative): {aa}");
                    Console.WriteLine($"  TC (Truncated): {tc}");
                    Console.WriteLine($"  RD (Recursion Desired): {rd}");
                    Console.WriteLine($"  RA (Recursion Available): {ra}");
                    Console.WriteLine($"  RCODE: {rcode}");
                    
                    Console.WriteLine($"Question Count: {questionCount}");
                    Console.WriteLine($"Answer Count: {answerCount}");
                    Console.WriteLine($"Authority Count: {authorityCount}");
                    Console.WriteLine($"Additional Count: {additionalCount}");
                    
                    // Verify Stage 2 requirements
                    Console.WriteLine("\n=== Stage 2 Verification ===");
                    bool stage2Valid = packetId == 1234 && qr && opcode == 0 && !aa && !tc && !rd && !ra && rcode == 0 &&
                                      questionCount == 0 && answerCount == 0 && authorityCount == 0 && additionalCount == 0;
                    
                    if (stage2Valid)
                    {
                        Console.WriteLine("✓ Response matches Stage 2 requirements perfectly!");
                    }
                    else
                    {
                        Console.WriteLine("✗ Response does not match Stage 2 requirements");
                    }
                }
                else
                {
                    Console.WriteLine($"✗ Expected 12-byte DNS header, got {result.Buffer.Length} bytes");
                }
            }
            else
            {
                Console.WriteLine("✗ Timeout waiting for server response");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Test failed: {ex.Message}");
        }
        
        Console.WriteLine("\n=== Test Complete ===");
    }
}
