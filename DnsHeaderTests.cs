// File: DnsHeaderTests.cs
// Unit tests for DNS header serialization

using System.Net;

public static class DnsHeaderTests
{
    public static void RunAllTests()
    {
        Console.WriteLine("=== Running DNS Header Tests ===");
        
        TestBasicHeaderSerialization();
        TestHardcodedStage2Values();
        TestFlagBitPacking();
        TestNetworkByteOrder();
        TestHeaderSize();
        
        Console.WriteLine("=== All DNS Header Tests Completed ===\n");
    }
    
    private static void TestBasicHeaderSerialization()
    {
        Console.WriteLine("Test 1: Basic Header Serialization");
        
        var message = new DnsMessage
        {
            Header = new DnsHeader
            {
                PacketIdentifier = 1234,
                QueryResponse = true,
                OpCode = 0,
                AuthoritativeAnswer = false,
                Truncation = false,
                RecursionDesired = false,
                RecursionAvailable = false,
                ResponseCode = 0,
                QuestionCount = 0,
                AnswerRecordCount = 0,
                AuthorityRecordCount = 0,
                AdditionalRecordCount = 0
            }
        };
        
        byte[] result = DnsPacketSerializer.ToByteArray(message);
        
        // Verify header is exactly 12 bytes
        if (result.Length == 12)
        {
            Console.WriteLine("✓ Header size is correct (12 bytes)");
        }
        else
        {
            Console.WriteLine($"✗ Header size is incorrect: {result.Length} bytes (expected 12)");
        }
        
        Console.WriteLine($"Serialized bytes: {BitConverter.ToString(result)}");
        Console.WriteLine();
    }
    
    private static void TestHardcodedStage2Values()
    {
        Console.WriteLine("Test 2: Stage 2 Hardcoded Values");
        
        var message = new DnsMessage
        {
            Header = new DnsHeader
            {
                PacketIdentifier = 1234,  // 0x04D2
                QueryResponse = true,     // QR = 1
                OpCode = 0,              // OPCODE = 0
                AuthoritativeAnswer = false, // AA = 0
                Truncation = false,      // TC = 0
                RecursionDesired = false, // RD = 0
                RecursionAvailable = false, // RA = 0
                ResponseCode = 0,        // RCODE = 0
                QuestionCount = 0,
                AnswerRecordCount = 0,
                AuthorityRecordCount = 0,
                AdditionalRecordCount = 0
            }
        };
        
        byte[] result = DnsPacketSerializer.ToByteArray(message);
        
        // Expected bytes for Stage 2:
        // Bytes 0-1: Packet ID 1234 (0x04D2) in network order = 0x04, 0xD2
        // Bytes 2-3: Flags with QR=1, others=0 = 0x8000 in network order = 0x80, 0x00
        // Bytes 4-11: All counts are 0 = 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
        
        byte[] expected = { 0x04, 0xD2, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
        
        bool matches = result.SequenceEqual(expected);
        if (matches)
        {
            Console.WriteLine("✓ Stage 2 hardcoded values are correctly serialized");
        }
        else
        {
            Console.WriteLine("✗ Stage 2 values don't match expected bytes");
            Console.WriteLine($"Expected: {BitConverter.ToString(expected)}");
            Console.WriteLine($"Actual:   {BitConverter.ToString(result)}");
        }
        Console.WriteLine();
    }
    
    private static void TestFlagBitPacking()
    {
        Console.WriteLine("Test 3: Flag Bit Packing");
        
        // Test various flag combinations
        var testCases = new[]
        {
            new { Name = "All flags false", QR = false, AA = false, TC = false, RD = false, RA = false, OpCode = (byte)0, RCode = (byte)0, Expected = (ushort)0x0000 },
            new { Name = "Only QR true", QR = true, AA = false, TC = false, RD = false, RA = false, OpCode = (byte)0, RCode = (byte)0, Expected = (ushort)0x8000 },
            new { Name = "QR and AA true", QR = true, AA = true, TC = false, RD = false, RA = false, OpCode = (byte)0, RCode = (byte)0, Expected = (ushort)0x8400 },
            new { Name = "All flags true with max values", QR = true, AA = true, TC = true, RD = true, RA = true, OpCode = (byte)15, RCode = (byte)15, Expected = (ushort)0xFF8F }
        };
        
        foreach (var testCase in testCases)
        {
            var message = new DnsMessage
            {
                Header = new DnsHeader
                {
                    PacketIdentifier = 0,
                    QueryResponse = testCase.QR,
                    OpCode = testCase.OpCode,
                    AuthoritativeAnswer = testCase.AA,
                    Truncation = testCase.TC,
                    RecursionDesired = testCase.RD,
                    RecursionAvailable = testCase.RA,
                    ResponseCode = testCase.RCode,
                    QuestionCount = 0,
                    AnswerRecordCount = 0,
                    AuthorityRecordCount = 0,
                    AdditionalRecordCount = 0
                }
            };
            
            byte[] result = DnsPacketSerializer.ToByteArray(message);
            ushort actualFlags = (ushort)IPAddress.NetworkToHostOrder(BitConverter.ToInt16(result, 2));
            
            if (actualFlags == testCase.Expected)
            {
                Console.WriteLine($"✓ {testCase.Name}: 0x{actualFlags:X4}");
            }
            else
            {
                Console.WriteLine($"✗ {testCase.Name}: Expected 0x{testCase.Expected:X4}, got 0x{actualFlags:X4}");
            }
        }
        Console.WriteLine();
    }
    
    private static void TestNetworkByteOrder()
    {
        Console.WriteLine("Test 4: Network Byte Order");
        
        var message = new DnsMessage
        {
            Header = new DnsHeader
            {
                PacketIdentifier = 0x1234,
                QueryResponse = false,
                OpCode = 0,
                AuthoritativeAnswer = false,
                Truncation = false,
                RecursionDesired = false,
                RecursionAvailable = false,
                ResponseCode = 0,
                QuestionCount = 0x5678,
                AnswerRecordCount = 0x9ABC,
                AuthorityRecordCount = 0xDEF0,
                AdditionalRecordCount = 0x1357
            }
        };
        
        byte[] result = DnsPacketSerializer.ToByteArray(message);
        
        // Check if multi-byte values are in network (big-endian) order
        ushort packetId = (ushort)((result[0] << 8) | result[1]);
        ushort questionCount = (ushort)((result[4] << 8) | result[5]);
        ushort answerCount = (ushort)((result[6] << 8) | result[7]);
        ushort authorityCount = (ushort)((result[8] << 8) | result[9]);
        ushort additionalCount = (ushort)((result[10] << 8) | result[11]);
        
        bool allCorrect = packetId == 0x1234 && 
                         questionCount == 0x5678 && 
                         answerCount == 0x9ABC && 
                         authorityCount == 0xDEF0 && 
                         additionalCount == 0x1357;
        
        if (allCorrect)
        {
            Console.WriteLine("✓ All multi-byte values are in correct network byte order");
        }
        else
        {
            Console.WriteLine("✗ Network byte order is incorrect");
            Console.WriteLine($"Packet ID: Expected 0x1234, got 0x{packetId:X4}");
            Console.WriteLine($"Question Count: Expected 0x5678, got 0x{questionCount:X4}");
            Console.WriteLine($"Answer Count: Expected 0x9ABC, got 0x{answerCount:X4}");
            Console.WriteLine($"Authority Count: Expected 0xDEF0, got 0x{authorityCount:X4}");
            Console.WriteLine($"Additional Count: Expected 0x1357, got 0x{additionalCount:X4}");
        }
        Console.WriteLine();
    }
    
    private static void TestHeaderSize()
    {
        Console.WriteLine("Test 5: Header Size Consistency");
        
        // Test multiple different header configurations to ensure size is always 12 bytes
        var testHeaders = new[]
        {
            new DnsHeader { PacketIdentifier = 0 },
            new DnsHeader { PacketIdentifier = 65535, QueryResponse = true, OpCode = 15, AuthoritativeAnswer = true, Truncation = true, RecursionDesired = true, RecursionAvailable = true, ResponseCode = 15, QuestionCount = 65535, AnswerRecordCount = 65535, AuthorityRecordCount = 65535, AdditionalRecordCount = 65535 },
            new DnsHeader { PacketIdentifier = 12345, QuestionCount = 1, AnswerRecordCount = 2 }
        };
        
        bool allCorrectSize = true;
        for (int i = 0; i < testHeaders.Length; i++)
        {
            var message = new DnsMessage { Header = testHeaders[i] };
            byte[] result = DnsPacketSerializer.ToByteArray(message);
            
            if (result.Length != 12)
            {
                Console.WriteLine($"✗ Header {i + 1}: Size is {result.Length} bytes (expected 12)");
                allCorrectSize = false;
            }
        }
        
        if (allCorrectSize)
        {
            Console.WriteLine("✓ All header configurations produce exactly 12 bytes");
        }
        Console.WriteLine();
    }
}
