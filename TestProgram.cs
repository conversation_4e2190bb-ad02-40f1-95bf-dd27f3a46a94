// File: TestProgram.cs
// Comprehensive test program for Stage 2 DNS implementation

using System.Net;
using System.Net.Sockets;

public static class TestProgram
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("=== DNS Server Stage 2 Comprehensive Testing ===\n");
        
        // Run unit tests first
        DnsHeaderTests.RunAllTests();
        
        // Test network communication
        await TestNetworkCommunication();
        
        // Test error handling
        TestErrorHandling();
        
        Console.WriteLine("=== All Tests Completed ===");
    }
    
    private static async Task TestNetworkCommunication()
    {
        Console.WriteLine("=== Network Communication Tests ===");
        
        // Start the DNS server in a background task
        var serverTask = Task.Run(async () =>
        {
            try
            {
                using var udpClient = new UdpClient(new IPEndPoint(IPAddress.Any, 2054)); // Use different port for testing
                Console.WriteLine("Test DNS server listening on port 2054...");
                
                while (true)
                {
                    UdpReceiveResult receiveResult = await udpClient.ReceiveAsync();
                    Console.WriteLine($"Test server received packet from {receiveResult.RemoteEndPoint}");
                    
                    // Create the same response as the main server
                    var responseMessage = new DnsMessage
                    {
                        Header = new DnsHeader
                        {
                            PacketIdentifier = 1234,
                            QueryResponse = true,
                            OpCode = 0,
                            AuthoritativeAnswer = false,
                            Truncation = false,
                            RecursionDesired = false,
                            RecursionAvailable = false,
                            ResponseCode = 0,
                            QuestionCount = 0,
                            AnswerRecordCount = 0,
                            AuthorityRecordCount = 0,
                            AdditionalRecordCount = 0
                        }
                    };
                    
                    byte[] responseBytes = DnsPacketSerializer.ToByteArray(responseMessage);
                    await udpClient.SendAsync(responseBytes, responseBytes.Length, receiveResult.RemoteEndPoint);
                    Console.WriteLine($"Test server sent header response to {receiveResult.RemoteEndPoint}");
                    break; // Exit after first response for testing
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test server error: {ex.Message}");
            }
        });
        
        // Give server time to start
        await Task.Delay(100);
        
        // Test client communication
        await TestClientCommunication();
        
        // Wait for server task to complete
        await serverTask;
        
        Console.WriteLine();
    }
    
    private static async Task TestClientCommunication()
    {
        Console.WriteLine("Test 6: Client-Server Communication");
        
        try
        {
            using var client = new UdpClient();
            var serverEndpoint = new IPEndPoint(IPAddress.Loopback, 2054);
            
            // Send a test packet
            byte[] testData = { 0x01, 0x02, 0x03, 0x04 };
            await client.SendAsync(testData, testData.Length, serverEndpoint);
            Console.WriteLine("✓ Sent test packet to server");
            
            // Receive response with timeout
            var receiveTask = client.ReceiveAsync();
            var timeoutTask = Task.Delay(5000); // 5 second timeout
            
            var completedTask = await Task.WhenAny(receiveTask, timeoutTask);
            
            if (completedTask == receiveTask)
            {
                var result = await receiveTask;
                Console.WriteLine($"✓ Received response from server: {result.Buffer.Length} bytes");
                Console.WriteLine($"Response bytes: {BitConverter.ToString(result.Buffer)}");
                
                // Verify the response is the expected DNS header
                byte[] expectedHeader = { 0x04, 0xD2, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
                
                if (result.Buffer.SequenceEqual(expectedHeader))
                {
                    Console.WriteLine("✓ Response matches expected DNS header format");
                }
                else
                {
                    Console.WriteLine("✗ Response does not match expected DNS header format");
                    Console.WriteLine($"Expected: {BitConverter.ToString(expectedHeader)}");
                    Console.WriteLine($"Actual:   {BitConverter.ToString(result.Buffer)}");
                }
            }
            else
            {
                Console.WriteLine("✗ Timeout waiting for server response");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Client communication test failed: {ex.Message}");
        }
    }
    
    private static void TestErrorHandling()
    {
        Console.WriteLine("=== Error Handling Tests ===");
        
        Console.WriteLine("Test 7: Null Message Handling");
        try
        {
            DnsMessage? nullMessage = null;
            byte[] result = DnsPacketSerializer.ToByteArray(nullMessage!);
            Console.WriteLine("✗ Should have thrown exception for null message");
        }
        catch (Exception)
        {
            Console.WriteLine("✓ Correctly handles null message");
        }
        
        Console.WriteLine("Test 8: Null Header Handling");
        try
        {
            var messageWithNullHeader = new DnsMessage { Header = null! };
            byte[] result = DnsPacketSerializer.ToByteArray(messageWithNullHeader);
            Console.WriteLine("✗ Should have thrown exception for null header");
        }
        catch (Exception)
        {
            Console.WriteLine("✓ Correctly handles null header");
        }
        
        Console.WriteLine("Test 9: Boundary Value Testing");
        var boundaryMessage = new DnsMessage
        {
            Header = new DnsHeader
            {
                PacketIdentifier = ushort.MaxValue,
                QueryResponse = true,
                OpCode = 15, // Max 4-bit value
                AuthoritativeAnswer = true,
                Truncation = true,
                RecursionDesired = true,
                RecursionAvailable = true,
                ResponseCode = 15, // Max 4-bit value
                QuestionCount = ushort.MaxValue,
                AnswerRecordCount = ushort.MaxValue,
                AuthorityRecordCount = ushort.MaxValue,
                AdditionalRecordCount = ushort.MaxValue
            }
        };
        
        try
        {
            byte[] result = DnsPacketSerializer.ToByteArray(boundaryMessage);
            if (result.Length == 12)
            {
                Console.WriteLine("✓ Correctly handles boundary values");
            }
            else
            {
                Console.WriteLine($"✗ Boundary value test failed: {result.Length} bytes");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Boundary value test failed with exception: {ex.Message}");
        }
        
        Console.WriteLine();
    }
}
