# PowerShell script to test DNS server
try {
    $udp = New-Object System.Net.Sockets.UdpClient
    $bytes = @(1,2,3,4,5,6,7,8,9,10,11,12)
    $endpoint = New-Object System.Net.IPEndPoint([System.Net.IPAddress]::Loopback, 2053)
    
    Write-Host "Sending test data to DNS server..."
    $sent = $udp.Send($bytes, $bytes.Length, $endpoint)
    Write-Host "Sent $sent bytes"
    
    Write-Host "Waiting for response..."
    $response = $udp.Receive([ref]$endpoint)
    Write-Host "Received response: $($response.Length) bytes"
    Write-Host "Response data: $([System.BitConverter]::ToString($response))"
    
    $udp.Close()
    Write-Host "Test completed successfully!"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
}
